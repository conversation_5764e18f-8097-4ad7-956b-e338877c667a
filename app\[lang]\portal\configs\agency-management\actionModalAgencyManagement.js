"use client";

import {
  Mo<PERSON>,
  Button,
  Form,
  Select,
  DatePicker,
  Spin,
  Transfer,
  Grid,
} from "antd";
import { CloseOutlined, SaveOutlined } from "@ant-design/icons";
import {
  formatDate,
  useResponseHandler,
  getDateFormat,
  getSpinIndicator,
  getModifiedProductNoTitles,
} from "@/components/tools";
import { useState, useEffect } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { postAddUpdateAgencyDefinition } from "@/services/agencyManagement";

export default function AgencyAddEditModal({
  dict,
  lang,
  isModalOpen,
  setIsModalOpen,
  selectedData,
  isEdit,
  broadcastChannel,
  requestBody,
  getAgencyManagementPaginatedList,
}) {
  const { handleResponse, handleError } = useResponseHandler();
  const [formLoading, setFormLoading] = useState(false);
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [agencyData, setAgencyData] = useState([]);
  const [targetKeys, setTargetKeys] = useState([]);
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();
  console.log(selectedData);
  dayjs.extend(utc);
  const addUpdateAgencyDefinitionFunc = async (values) => {
    try {
      setFormLoading(true);
      const res = await postAddUpdateAgencyDefinition(lang, values);
      handleResponse(res);
      if (res?.status === "SUCCESS") {
        await getAgencyManagementPaginatedList(requestBody);
        setIsModalOpen(false);
      }
    } catch (error) {
      handleError(error, dict.public.error);
    } finally {
      setFormLoading(false);
    }
  };

  useEffect(() => {
    getAgencies();
    if (isEdit) {
      form.setFieldsValue({
        agencyCode: selectedData?.channel.toString(),
        begDate: selectedData?.begDate ? dayjs(selectedData.begDate) : null,
        endDate: selectedData?.endDate ? dayjs(selectedData.endDate) : null,
      });
    } else {
      form.resetFields();
    }
  }, [isEdit, selectedData]);
  const onFinish = async (values) => {
    values.begDate = dayjs
      .utc(
        dayjs(values?.begDate).format("DD/MM/YYYY 00:00:00"),
        "DD/MM/YYYY HH:mm:ss"
      )
      .toISOString();

    values.endDate = values.endDate
      ? dayjs
          .utc(
            dayjs(values?.endDate).format("DD/MM/YYYY 00:00:00"),
            "DD/MM/YYYY HH:mm:ss"
          )
          .toISOString()
      : null;
    const formData = {
      begDate: values.begDate,
      endDate: values.endDate,
      agencyCode: isEdit ? [values.agencyCode] : values.agencyCode,
      id: selectedData?.id ?? null,
      productNo: selectedData?.productNo,
    };
    await addUpdateAgencyDefinitionFunc(formData);
  };
  const getAgencies = () => {
    const tempTargetKeys = [];
    const tempMockData = [];
    broadcastChannel?.forEach((item) => {
      tempMockData.push({
        key: item.key,
        title: item.value,
        description: item.value,
        chosen: false,
      });
    });
    setAgencyData(tempMockData);
    setTargetKeys(tempTargetKeys);
  };
  console.log(formValues, "formvalues");
  const handleChange = (newTargetKeys, direction, moveKeys) => {
    console.log(newTargetKeys, direction, moveKeys);
    setTargetKeys(newTargetKeys);
  };
  const renderItem = (item) => {
    const customLabel = (
      <span className="custom-item">
        {item.title} - {item.description}
      </span>
    );
    return {
      label: customLabel, // for displayed item
      value: item.title, // for title and filter matching
    };
  };
  return (
    <Modal
      className="customize-modal"
      centered
      closable={false}
      maskClosable={false}
      open={isModalOpen}
      footer={false}
      width={1000}
      title={
        <div className="bg-azure flex min-h-12 justify-between gap-2">
          <h3 className="my-3 ms-4 flex flex-wrap items-center gap-4 leading-4">
            {!isEdit
              ? `${dict.configs.agencyManagement.addAgency}`
              : `${dict.configs.agencyManagement.editAgency}`}
            {`| ${getModifiedProductNoTitles(dict)[selectedData?.productNo] ?? ""}`}
          </h3>
          <div className="flex flex-wrap">
            <CloseOutlined
              className="cursor-pointer px-4"
              onClick={() => setIsModalOpen(false)}
            />
          </div>
        </div>
      }
    >
      <Spin indicator={getSpinIndicator} spinning={formLoading}>
        <Form
          form={form}
          layout="vertical"
          className="w-full"
          onFinish={onFinish}
        >
          <div className="rounded-xl p-6">
            <div className="flex flex-wrap gap-x-6">
              <Form.Item
                name="begDate"
                data-testid="begDate"
                className="!shrink !grow !basis-52"
                label={dict.public.startDate}
                rules={[
                  {
                    required: true,

                    message: `${dict.public.requiredField}`,
                  },
                ]}
              >
                <DatePicker
                  className="w-full"
                  format={getDateFormat[lang]}
                  disabledDate={(d) =>
                    formValues["endDate"]
                      ? !d || d.isAfter(formValues["endDate"])
                      : false
                  }
                />
              </Form.Item>
              <Form.Item
                name="endDate"
                data-testid="endDate"
                className="!shrink !grow !basis-52"
                label={dict.public.endDate}
              >
                <DatePicker
                  className="w-full"
                  format={getDateFormat[lang]}
                  disabledDate={(d) =>
                    formValues["begDate"]
                      ? !d || d.isBefore(formValues["begDate"])
                      : false
                  }
                />
              </Form.Item>
            </div>

            <Form.Item
              name="agencyCode"
              data-testid="agencyCode"
              className="w-full !shrink !grow"
              label={dict.configs.agencyManagement.agencySelection}
              rules={[
                {
                  required: true,

                  message: `${dict.public.requiredField}`,
                },
              ]}
            >
              {isEdit ? (
                <Select
                  data-testid="agencyCodeSelect"
                  //value={selectedData?.agencyCode}
                  disabled={isEdit}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label.toLowerCase() ?? "").includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                  options={broadcastChannel?.map((product) => {
                    return {
                      value: product.key,
                      label: `${product.value}`,
                    };
                  })}
                />
              ) : (
                <Transfer
                  titles={[
                    dict.configs.productManagement.agencies,
                    dict.configs.agencyManagement.selectedAgencies,
                  ]}
                  showSearch
                  dataSource={agencyData}
                  listStyle={{
                    width: screens.md ? "45%" : "100%", // 100% on small screens, 45% on medium and up
                    height: 300,
                  }}
                  targetKeys={targetKeys}
                  onChange={handleChange}
                  render={renderItem}
                />
              )}
            </Form.Item>

            <div className="flex justify-end gap-x-6">
              <Button
                data-testid="saveButton"
                type="primary"
                htmlType="submit"
                onClick={() => {}}
                icon={
                  <SaveOutlined
                    className={"inline-block h-5 w-5 align-text-top"}
                  />
                }
              >
                {dict.public.save}
              </Button>
            </div>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
}
