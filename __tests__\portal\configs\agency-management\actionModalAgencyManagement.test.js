// import {
//   render,
//   screen,
//   fireEvent,
//   waitFor,
//   act,
// } from "@testing-library/react";
// import AgencyAddEditModal from "@/app/[lang]/portal/configs/agency-management/actionModalAgencyManagement";
// // Update the path as necessary
// import { postAddUpdateAgencyDefinition } from "@/services/agencyManagement";
// import { useResponseHandler } from "@/components/tools";

// jest.mock("@/services/agencyManagement", () => ({
//   postAddUpdateAgencyDefinition: jest.fn(),
// }));

// jest.mock("@/components/tools", () => ({
//   useResponseHandler: jest.fn(),
// }));

// describe("AgencyAddEditModal", () => {
//   const mockSetIsModalOpen = jest.fn();
//   const mockGetAgencyManagementPaginatedList = jest.fn();
//   const mockHandleResponse = jest.fn();
//   const mockHandleError = jest.fn();

//   beforeEach(() => {
//     jest.clearAllMocks();
//     useResponseHandler.mockReturnValue({
//       handleResponse: mockHandleResponse,
//       handleError: mockHandleError,
//     });
//   });

//   test("renders correctly in add mode", () => {
//     render(
//       <AgencyAddEditModal
//         dict={{ public: { requiredField: "Required", save: "Save" } }}
//         lang="en"
//         isModalOpen={true}
//         setIsModalOpen={mockSetIsModalOpen}
//         selectedData={null}
//         isEdit={false}
//         broadcastChannel={[]}
//         requestBody={{}}
//         getAgencyManagementPaginatedList={mockGetAgencyManagementPaginatedList}
//       />
//     );

//     expect(screen.getByText("Save")).toBeInTheDocument();
//   });

//   test("renders correctly in edit mode", () => {
//     render(
//       <AgencyAddEditModal
//         dict={{ public: { requiredField: "Required", save: "Save" } }}
//         lang="en"
//         isModalOpen={true}
//         setIsModalOpen={mockSetIsModalOpen}
//         selectedData={{
//           channel: "CH1",
//           begDate: "2022-01-01",
//           endDate: "2022-12-31",
//         }}
//         isEdit={true}
//         broadcastChannel={[]}
//         requestBody={{}}
//         getAgencyManagementPaginatedList={mockGetAgencyManagementPaginatedList}
//       />
//     );

//     expect(screen.getByText("Save")).toBeInTheDocument();
//   });

//   test("submits form with correct values", async () => {
//     postAddUpdateAgencyDefinition.mockResolvedValue({ status: "SUCCESS" });

//     render(
//       <AgencyAddEditModal
//         dict={{ public: { requiredField: "Required", save: "Save" } }}
//         lang="en"
//         isModalOpen={true}
//         setIsModalOpen={mockSetIsModalOpen}
//         selectedData={null}
//         isEdit={false}
//         broadcastChannel={[{ key: "CH1", value: "Channel 1" }]}
//         requestBody={{}}
//         getAgencyManagementPaginatedList={mockGetAgencyManagementPaginatedList}
//       />
//     );

//     fireEvent.change(screen.getByTestId("begDate").querySelector("input"), {
//       target: { value: "01/01/2022" },
//     });

//     fireEvent.change(screen.getByTestId("endDate").querySelector("input"), {
//       target: { value: "31/12/2022" },
//     });

//     fireEvent.change(screen.getByTestId("agencyCode").querySelector("input"), {
//       target: { value: "CH1" },
//     });

//     fireEvent.click(screen.getByTestId("saveButton"));

//     await waitFor(() => {
//       expect(postAddUpdateAgencyDefinition).toHaveBeenCalledWith(
//         "en",
//         expect.objectContaining({
//           begDate: expect.any(String),
//           endDate: expect.any(String),
//           agencyCode: ["CH1"],
//         })
//       );
//       expect(mockGetAgencyManagementPaginatedList).toHaveBeenCalled();
//       expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
//     });
//   });

//   test("handles form submission error", async () => {
//     postAddUpdateAgencyDefinition.mockRejectedValue(new Error("Error"));

//     render(
//       <AgencyAddEditModal
//         dict={{
//           public: { requiredField: "Required", save: "Save", error: "Error" },
//         }}
//         lang="en"
//         isModalOpen={true}
//         setIsModalOpen={mockSetIsModalOpen}
//         selectedData={null}
//         isEdit={false}
//         broadcastChannel={[{ key: "CH1", value: "Channel 1" }]}
//         requestBody={{}}
//         getAgencyManagementPaginatedList={mockGetAgencyManagementPaginatedList}
//       />
//     );

//     fireEvent.change(screen.getByTestId("begDate").querySelector("input"), {
//       target: { value: "01/01/2022" },
//     });

//     fireEvent.click(screen.getByTestId("saveButton"));

//     await waitFor(() => {
//       expect(mockHandleError).toHaveBeenCalledWith(expect.any(Error), "Error");
//     });
//   });

//   // test("closes the modal", async () => {
//   //   render(
//   //     <AgencyAddEditModal
//   //       dict={{ public: { requiredField: "Required", save: "Save" } }}
//   //       lang="en"
//   //       isModalOpen={true}
//   //       setIsModalOpen={mockSetIsModalOpen}
//   //       selectedData={null}
//   //       isEdit={false}
//   //       broadcastChannel={[{ key: "CH1", value: "Channel 1" }]}
//   //       requestBody={{}}
//   //       getAgencyManagementPaginatedList={mockGetAgencyManagementPaginatedList}
//   //     />
//   //   );

//   //   fireEvent.click(screen.getByRole("button", { name: /close/i }));

//   //   expect(mockSetIsModalOpen).toHaveBeenCalledWith(false);
//   // });
// });

import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
  cleanup,
} from "@testing-library/react";
import { getDictionary } from "@/dictionaries";
import { postAddUpdateAgencyDefinition } from "@/services/agencyManagement";
import AgencyAddEditModal from "@/app/[lang]/portal/configs/agency-management/actionModalAgencyManagement";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

jest.mock("@/services/agencyManagement", () => ({
  postAddUpdateAgencyDefinition: jest.fn(),
}));

jest.mock("@/components/tools", () => ({
  useResponseHandler: jest.fn(() => ({
    handleResponse: jest.fn(),
    handleError: jest.fn(),
  })),
  getDateFormat: { en: "MM/DD/YYYY", tr: "DD/MM/YYYY" },
  getSpinIndicator: <div>Spinner</div>,
  formatDate: jest.fn(),
  getModifiedProductNoTitles: jest.fn(),
}));

describe("AgencyAddEditModal", () => {
  Object.keys({ en: "en", tr: "tr" }).forEach((lang) => {
    describe(`Language: ${lang}`, () => {
      let dict, mockGetAgencyManagementPaginatedList;

      beforeAll(async () => {
        dict = await getDictionary(lang);
        dayjs.extend(utc);
      });

      // Clean up the DOM before each test to prevent duplicate IDs
      beforeEach(() => {
        cleanup(); // Ensure the DOM is reset before each test
        jest.clearAllMocks();
        mockGetAgencyManagementPaginatedList = jest.fn();
        postAddUpdateAgencyDefinition.mockResolvedValue({ status: "SUCCESS" });
      });

      // Render the component only when needed within each test
      const renderComponent = (props = {}) => {
        return render(
          <AgencyAddEditModal
            dict={dict}
            lang={lang}
            isModalOpen={true}
            setIsModalOpen={jest.fn()}
            selectedData={{ productNo: "P001", id: 1, channel: "CH001" }}
            isEdit={false}
            broadcastChannel={[{ key: "CH001", value: "Channel 1" }]}
            requestBody={{ productNo: "P001" }}
            getAgencyManagementPaginatedList={
              mockGetAgencyManagementPaginatedList
            }
            {...props}
          />
        );
      };

      test("form:Components should be present in the modal", async () => {
        await act(async () => {
          renderComponent();
        });

        expect(screen.getByTestId("agencyCode")).toBeInTheDocument();
        expect(screen.getByTestId("begDate")).toBeInTheDocument();
        expect(screen.getByTestId("endDate")).toBeInTheDocument();
        expect(screen.getByTestId("saveButton")).toBeInTheDocument();
      });

      test("form:Text fields should be correct", async () => {
        await act(async () => {
          renderComponent();
        });

        const texts = [
          dict.configs.agencyManagement.addAgency,
          dict.configs.agencyManagement.agencySelection,
          dict.public.startDate,
          dict.public.endDate,
          dict.public.save,
        ];
        texts.forEach((text) => {
          let elements = screen.getAllByText(new RegExp(text, "i"));
          elements.forEach((element) => {
            expect(element).toBeInTheDocument();
          });
        });
      });

      // test("form:Should populate fields in edit mode", async () => {
      //   await act(async () => {
      //     renderComponent({
      //       isEdit: true,
      //       selectedData: {
      //         productNo: "101",
      //         id: 1,
      //         channel: "CH001",
      //         begDate: "2025-01-01T00:00:00Z",
      //         endDate: "2025-12-31T00:00:00Z",
      //       },
      //     });
      //   });

      //   //const agencyCodeSelect = screen.getByTestId("agencyCode");
      //   // .querySelector(".ant-select-selection-item");
      //   //expect(agencyCodeSelect).toHaveTextContent("Channel 1");
      //   //expect(screen.getByTestId("agencyCodeSelect")).toBeInTheDocument();
      // });

      //   test("form:Save button should submit form with correct values", async () => {
      //     const setIsModalOpen = jest.fn();
      //     await act(async () => {
      //       renderComponent({
      //         setIsModalOpen,
      //         selectedData: { productNo: "P001" },
      //         isEdit: false,
      //       });
      //     });

      //     const agencyCodeSelect = screen
      //       .getByTestId("agencyCode")
      //       .querySelector(".ant-select-selector");
      //     const begDatePicker = screen
      //       .getByTestId("begDate")
      //       .querySelector("input");
      //     const saveButton = screen.getByTestId("saveButton");

      //     await act(async () => {
      //       //  fireEvent.mouseDown(agencyCodeSelect);
      //       fireEvent.click(screen.getByText("Channel 1"));
      //       fireEvent.change(begDatePicker, { target: { value: "01/01/2025" } });
      //       fireEvent.click(saveButton);
      //     });

      //     await waitFor(() => {
      //       expect(postAddUpdateAgencyDefinition).toHaveBeenCalledWith(
      //         lang,
      //         expect.objectContaining({
      //           agencyCode: "CH001",
      //           productNo: "P001",
      //           begDate: expect.any(String),
      //           endDate: null,
      //           id: null,
      //         })
      //       );
      //       expect(mockGetAgencyManagementPaginatedList).toHaveBeenCalled();
      //       expect(setIsModalOpen).toHaveBeenCalledWith(false);
      //     });
      //   });

      //   test("form:Should reset fields when not in edit mode", async () => {
      //     await act(async () => {
      //       renderComponent({
      //         selectedData: { productNo: "P001" },
      //         isEdit: false,
      //       });
      //     });

      //     const agencyCodeSelect = screen
      //       .getByTestId("agencyCode")
      //       .querySelector(".ant-select-selection-item");
      //     const begDatePicker = screen
      //       .getByTestId("begDate")
      //       .querySelector("input");
      //     const endDatePicker = screen
      //       .getByTestId("endDate")
      //       .querySelector("input");

      //     expect(agencyCodeSelect).toBeNull();
      //     expect(begDatePicker).toHaveValue("");
      //     expect(endDatePicker).toHaveValue("");
      //   });
    });
  });
});
